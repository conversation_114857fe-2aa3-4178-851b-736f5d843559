<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.connect.ConnectFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Connection Status -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Connection Status"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/text_connection_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Disconnected"
                    android:textSize="16sp"
                    android:textColor="@android:color/holo_red_dark" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Device Selection -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="USB Serial Device"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/spinner_devices"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp" />

                <Button
                    android:id="@+id/button_refresh_devices"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Refresh Devices"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Baud Rate Selection -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Baud Rate"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/spinner_baud_rate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Connection Controls -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/button_connect"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="Connect" />

            <Button
                android:id="@+id/button_disconnect"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="Disconnect"
                android:enabled="false"
                style="@style/Widget.Material3.Button.OutlinedButton" />

        </LinearLayout>

        <!-- Data Display -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp"
            app:cardCornerRadius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Received Data"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <Button
                        android:id="@+id/button_clear_data"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Clear"
                        style="@style/Widget.Material3.Button.TextButton" />

                </LinearLayout>

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:background="@android:color/black"
                    android:padding="8dp">

                    <TextView
                        android:id="@+id/text_received_data"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@android:color/white"
                        android:textSize="12sp"
                        android:fontFamily="monospace"
                        android:text="No data received..." />

                </ScrollView>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Send Data -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardElevation="4dp"
            app:cardCornerRadius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Send Data"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_send_data"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="Enter data to send..." />

                    </com.google.android.material.textfield.TextInputLayout>

                    <Button
                        android:id="@+id/button_send_data"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Send"
                        android:enabled="false" />

                </LinearLayout>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>
</ScrollView>
