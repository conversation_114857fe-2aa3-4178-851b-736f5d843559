package com.sartorua.thrust_tester.usb

import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.util.Log
import androidx.core.content.ContextCompat
import com.hoho.android.usbserial.driver.UsbSerialDriver
import com.hoho.android.usbserial.driver.UsbSerialPort
import com.hoho.android.usbserial.driver.UsbSerialProber
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.IOException

class UsbSerialManager(private val context: Context) {
    
    companion object {
        private const val TAG = "UsbSerialManager"
        private const val ACTION_USB_PERMISSION = "com.sartorua.thrust_tester.USB_PERMISSION"
        private const val READ_WAIT_MILLIS = 2000
        private const val WRITE_WAIT_MILLIS = 2000
    }
    
    private val usbManager = context.getSystemService(Context.USB_SERVICE) as UsbManager
    private var usbSerialPort: UsbSerialPort? = null
    private var readJob: Job? = null
    
    // State flows for UI updates
    private val _connectionState = MutableStateFlow(ConnectionState.DISCONNECTED)
    val connectionState: StateFlow<ConnectionState> = _connectionState.asStateFlow()
    
    private val _availableDevices = MutableStateFlow<List<UsbSerialDevice>>(emptyList())
    val availableDevices: StateFlow<List<UsbSerialDevice>> = _availableDevices.asStateFlow()
    
    private val _receivedData = MutableStateFlow("")
    val receivedData: StateFlow<String> = _receivedData.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // USB permission receiver
    private val usbReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                ACTION_USB_PERMISSION -> {
                    synchronized(this) {
                        val device: UsbDevice? = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE)
                        if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                            device?.let { connectToDevice(it) }
                        } else {
                            Log.d(TAG, "Permission denied for device $device")
                            _errorMessage.value = "USB permission denied"
                        }
                    }
                }
                UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                    Log.d(TAG, "USB device attached")
                    refreshDeviceList()
                }
                UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                    Log.d(TAG, "USB device detached")
                    disconnect()
                    refreshDeviceList()
                }
            }
        }
    }
    
    init {
        // Register USB receiver
        val filter = IntentFilter().apply {
            addAction(ACTION_USB_PERMISSION)
            addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
            addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
        }
        context.registerReceiver(usbReceiver, filter)
        refreshDeviceList()
    }
    
    fun refreshDeviceList() {
        val availableDrivers = UsbSerialProber.getDefaultProber().findAllDrivers(usbManager)
        val devices = availableDrivers.map { driver ->
            UsbSerialDevice(
                device = driver.device,
                driver = driver,
                name = "${driver.device.deviceName} (${driver.javaClass.simpleName})",
                vendorId = driver.device.vendorId,
                productId = driver.device.productId
            )
        }
        _availableDevices.value = devices
        Log.d(TAG, "Found ${devices.size} USB serial devices")
    }
    
    fun requestConnection(device: UsbSerialDevice, baudRate: Int = 9600) {
        if (usbManager.hasPermission(device.device)) {
            connectToDevice(device.device, baudRate)
        } else {
            val permissionIntent = PendingIntent.getBroadcast(
                context, 0, Intent(ACTION_USB_PERMISSION), 
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            usbManager.requestPermission(device.device, permissionIntent)
        }
    }
    
    private fun connectToDevice(device: UsbDevice, baudRate: Int = 9600) {
        try {
            _connectionState.value = ConnectionState.CONNECTING
            
            val driver = UsbSerialProber.getDefaultProber().probeDevice(device)
            if (driver == null) {
                _errorMessage.value = "No driver found for device"
                _connectionState.value = ConnectionState.DISCONNECTED
                return
            }
            
            val connection = usbManager.openDevice(driver.device)
            if (connection == null) {
                _errorMessage.value = "Failed to open device connection"
                _connectionState.value = ConnectionState.DISCONNECTED
                return
            }
            
            usbSerialPort = driver.ports[0].apply {
                open(connection)
                setParameters(baudRate, 8, UsbSerialPort.STOPBITS_1, UsbSerialPort.PARITY_NONE)
            }
            
            _connectionState.value = ConnectionState.CONNECTED
            _errorMessage.value = null
            startReading()
            
            Log.d(TAG, "Connected to ${device.deviceName} at $baudRate baud")
            
        } catch (e: IOException) {
            Log.e(TAG, "Error connecting to device", e)
            _errorMessage.value = "Connection failed: ${e.message}"
            _connectionState.value = ConnectionState.DISCONNECTED
        }
    }
    
    private fun startReading() {
        readJob?.cancel()
        readJob = CoroutineScope(Dispatchers.IO).launch {
            val buffer = ByteArray(8192)
            while (isActive && usbSerialPort != null) {
                try {
                    val numBytesRead = usbSerialPort?.read(buffer, READ_WAIT_MILLIS) ?: 0
                    if (numBytesRead > 0) {
                        val data = String(buffer, 0, numBytesRead)
                        _receivedData.value = _receivedData.value + data
                        Log.d(TAG, "Received: $data")
                    }
                } catch (e: IOException) {
                    if (isActive) {
                        Log.e(TAG, "Error reading from serial port", e)
                        _errorMessage.value = "Read error: ${e.message}"
                        break
                    }
                }
            }
        }
    }
    
    fun sendData(data: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                usbSerialPort?.write(data.toByteArray(), WRITE_WAIT_MILLIS)
                Log.d(TAG, "Sent: $data")
            } catch (e: IOException) {
                Log.e(TAG, "Error writing to serial port", e)
                _errorMessage.value = "Write error: ${e.message}"
            }
        }
    }
    
    fun clearReceivedData() {
        _receivedData.value = ""
    }
    
    fun clearError() {
        _errorMessage.value = null
    }
    
    fun disconnect() {
        readJob?.cancel()
        try {
            usbSerialPort?.close()
        } catch (e: IOException) {
            Log.e(TAG, "Error closing serial port", e)
        }
        usbSerialPort = null
        _connectionState.value = ConnectionState.DISCONNECTED
        Log.d(TAG, "Disconnected from USB serial device")
    }
    
    fun cleanup() {
        disconnect()
        try {
            context.unregisterReceiver(usbReceiver)
        } catch (e: IllegalArgumentException) {
            // Receiver was not registered
        }
    }
}

enum class ConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED
}

data class UsbSerialDevice(
    val device: UsbDevice,
    val driver: UsbSerialDriver,
    val name: String,
    val vendorId: Int,
    val productId: Int
)
